import 'dart:ui';

/// A class to hold information about a user registered in the system.
class RegisteredUser {
  final String id;
  final String name;

  /// The face embedding data.
  final List<double> embedding;

  RegisteredUser({
    required this.id,
    required this.name,
    required this.embedding,
  });
}

/// A class to hold the result of a face match against a registered user.
class FaceMatch {
  /// The bounding box of the detected face in the image.
  final Rect boundingRect;

  /// The registered user that was matched.
  final RegisteredUser user;

  /// A score indicating the quality or confidence of the match (e.g., distance).
  final double difference;

  /// A flag indicating if the face was recognized against the registry.
  final bool isRecognized;

  FaceMatch({
    required this.boundingRect,
    required this.user,
    required this.difference,
    required this.isRecognized,
  });
}

class FaceRegistry {
  /// The list of registered users.
  static List<RegisteredUser> listOfUsers = [];

  /// Finds a matching user from the registered list for a given face embedding.
  static FaceMatch findFromList(List<double> newEmbedding, Rect boundingBox) {
    RegisteredUser? bestMatchUser;
    double lowestDifference = double.infinity;

    for (final registeredUser in listOfUsers) {
      final double difference = _calculateDifference(
        registeredUser.embedding,
        newEmbedding,
      );
      if (difference < lowestDifference) {
        lowestDifference = difference;
        bestMatchUser = registeredUser;
      }
    }

    // Threshold for considering a match "recognized".
    // This value may need tuning.
    const double recognitionThreshold = 1.0;

    if (lowestDifference < recognitionThreshold && bestMatchUser != null) {
      return FaceMatch(
        boundingRect: boundingBox,
        user: bestMatchUser,
        difference: lowestDifference,
        isRecognized: true,
      );
    }
    // Return an "unknown" match if no one is close enough.
    // The embedding of the unknown face is stored for potential registration.
    return FaceMatch(
      boundingRect: boundingBox,
      user: RegisteredUser(
        id: 'unknown',
        name: 'Unknown',
        embedding: newEmbedding,
      ),
      difference: lowestDifference,
      isRecognized: false,
    );
  }

  /// Calculates the squared Euclidean distance between two embeddings.
  static double _calculateDifference(List<double> e1, List<double> e2) {
    double sum = 0.0;
    for (int i = 0; i < e1.length; i++) {
      sum += (e1[i] - e2[i]) * (e1[i] - e2[i]);
    }
    return sum;
  }
}
