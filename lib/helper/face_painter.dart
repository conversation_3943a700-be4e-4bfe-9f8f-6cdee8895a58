import 'package:flutter/material.dart';
import 'dart:ui' as ui;

import '../models/face_registry.dart';
import 'cordinates_translator.dart';

class FaceDetectorPainter extends CustomPainter {
  FaceDetectorPainter(this.faces, this.imageSize, this.image);
  final ui.Image image;
  final List<FaceMatch> faces;
  final Size imageSize;

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the image first
    canvas.drawImage(image, Offset.zero, Paint());

    // Define paints and styles outside the loop for performance
    final boxPaint = Paint()
      ..style = PaintingStyle.stroke
      ..color = Colors.green
      ..strokeWidth = 1.7;

    final textBackgroundPaint = Paint()..color = Colors.blue;

    final textStyle = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      background: textBackgroundPaint,
      color: Colors.white,
    );

    for (final FaceMatch faceMeta in faces) {
      final faceBounding = faceMeta.boundingRect;
      final person = faceMeta.user;
      final accuracy = faceMeta.difference;
      final left = translateX(faceBounding.left, size, imageSize);
      final top = translateY(faceBounding.top, size, imageSize);
      final right = translateX(faceBounding.right, size, imageSize);
      final bottom = translateY(faceBounding.bottom, size, imageSize);

      // Draw the bounding box for the face
      final rect = Rect.fromLTRB(left, top, right, bottom);
      canvas.drawRect(rect, boxPaint);

      // Create and layout the text painter to display info
      final textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        text: TextSpan(
          // Display the name and the accuracy (difference score)
          text:
              "Name: ${person.name}\nAccuracy: ${accuracy.toStringAsFixed(2)}",
          style: textStyle,
        ),
      )..layout(maxWidth: right - left);

      // Position the text above the bounding box
      final offset = Offset(left, top - textPainter.height - 5);
      textPainter.paint(canvas, offset);
    }
  }

  @override
  bool shouldRepaint(FaceDetectorPainter oldDelegate) {
    return oldDelegate.imageSize != imageSize || oldDelegate.faces != faces;
  }
}
