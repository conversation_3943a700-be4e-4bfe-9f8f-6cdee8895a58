import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:camera/camera.dart';
import 'package:face_recognition/helper/face_painter.dart';
import 'package:face_recognition/helper/face_recognition.dart';
import 'package:face_recognition/models/face_registry.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:permission_handler/permission_handler.dart';

class LiveFaceCaptureView extends StatefulWidget {
  const LiveFaceCaptureView({super.key});

  @override
  State<LiveFaceCaptureView> createState() => _LiveFaceCaptureViewState();
}

class _LiveFaceCaptureViewState extends State<LiveFaceCaptureView> {
  CameraController? _cameraController;
  final _faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      performanceMode: FaceDetectorMode.fast,
      enableTracking: true,
    ),
  );
  final _faceRecognition = FaceRecognition();
  bool _isDetecting = false;
  bool _isInitializing = true;
  String? _errorMessage;
  XFile? _capturedImageFile;
  List<FaceMatch> _detectedFaces = [];
  ui.Image? _displayImage;
  Size? _imageSize;
  Timer? _noFaceTimer;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Check camera permissions first
      await _checkCameraPermissions();

      // Load the face recognition model
      _faceRecognition.loadModel();

      // Initialize camera
      await _initializeCamera();

      // Start no-face detection timer (30 seconds timeout)
      _startNoFaceTimer();

      setState(() {
        _isInitializing = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize: ${e.toString()}';
        _isInitializing = false;
      });
    }
  }

  Future<void> _checkCameraPermissions() async {
    final status = await Permission.camera.status;

    if (status.isDenied) {
      final result = await Permission.camera.request();
      if (result.isDenied) {
        throw Exception('Camera permission is required for face detection');
      }
    }

    if (status.isPermanentlyDenied) {
      throw Exception(
        'Camera permission is permanently denied. Please enable it in device settings.',
      );
    }
  }

  void _startNoFaceTimer() {
    _noFaceTimer?.cancel();
    _noFaceTimer = Timer(const Duration(seconds: 30), () {
      if (_capturedImageFile == null && mounted) {
        setState(() {
          _errorMessage =
              'No face detected within 30 seconds. Please try again.';
        });
      }
    });
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();

      if (cameras.isEmpty) {
        throw Exception('No cameras available on this device');
      }

      // Use the front camera (selfie camera) exclusively
      final cameraDescription = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () =>
            throw Exception('Front camera not available on this device'),
      );

      _cameraController = CameraController(
        cameraDescription,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: Platform.isAndroid
            ? ImageFormatGroup
                  .nv21 // for Android
            : ImageFormatGroup.bgra8888, // for iOS
      );

      await _cameraController!.initialize();
      if (!mounted) return;

      // Start streaming images for face detection
      _cameraController!.startImageStream(_processCameraImage);

      setState(() {});
    } catch (e) {
      throw Exception('Camera initialization failed: ${e.toString()}');
    }
  }

  void _processCameraImage(CameraImage image) async {
    if (_isDetecting || _capturedImageFile != null) return;

    _isDetecting = true;

    try {
      final inputImage = _inputImageFromCameraImage(image);
      if (inputImage == null) {
        _isDetecting = false;
        return;
      }

      final faces = await _faceDetector.processImage(inputImage);

      if (faces.isNotEmpty && _capturedImageFile == null) {
        // Face found! Cancel the no-face timer and capture the image
        _noFaceTimer?.cancel();

        try {
          // Stop the stream and capture the image
          await _cameraController?.stopImageStream();
          final XFile picture = await _cameraController!.takePicture();

          // Process the captured image with face recognition
          final faceMatches = await _getFaceMatches(picture, faces);
          final ui.Image displayImage = await _loadImage(picture);

          setState(() {
            _capturedImageFile = picture;
            _detectedFaces = faceMatches;
            _displayImage = displayImage;
            // Store the image size for the painter. The image from the stream and the
            // final captured image might have different dimensions on some devices.
            // We use the final image's dimensions.
            _imageSize = Size(
              displayImage.width.toDouble(),
              displayImage.height.toDouble(),
            );
          });
        } catch (captureError) {
          // Handle capture errors
          setState(() {
            _errorMessage =
                'Failed to capture image: ${captureError.toString()}';
          });
        }
      }
    } catch (detectionError) {
      // Handle face detection errors
      if (mounted) {
        setState(() {
          _errorMessage = 'Face detection failed: ${detectionError.toString()}';
        });
      }
    } finally {
      _isDetecting = false;
    }
  }

  // Integrate with the actual face recognition system
  Future<List<FaceMatch>> _getFaceMatches(
    XFile image,
    List<Face> detectedFaces,
  ) async {
    try {
      // Decode the captured image
      final imageBytes = await image.readAsBytes();
      final decodedImage = img.decodeImage(imageBytes);

      if (decodedImage == null) {
        throw Exception('Failed to decode captured image');
      }

      List<FaceMatch> matches = [];

      for (var face in detectedFaces) {
        // Generate facial vector using the existing face recognition system
        final facialVector = await _faceRecognition.recognizeFace(
          decodedImage,
          face,
        );

        // Find match from registered users
        final faceMatch = FaceRegistry.findFromList(
          facialVector,
          face.boundingBox,
        );

        matches.add(faceMatch);
      }

      return matches;
    } catch (e) {
      // Return unknown face match in case of error
      return detectedFaces
          .map(
            (face) => FaceMatch(
              boundingRect: face.boundingBox,
              user: RegisteredUser(
                id: "error",
                name: "Recognition Error",
                embedding: [],
              ),
              difference: double.infinity,
              isRecognized: false,
            ),
          )
          .toList();
    }
  }

  Future<ui.Image> _loadImage(XFile file) async {
    final data = await file.readAsBytes();
    return await decodeImageFromList(data);
  }

  InputImage? _inputImageFromCameraImage(CameraImage image) {
    final camera = _cameraController!.description;
    final sensorOrientation = camera.sensorOrientation;
    final rotation = InputImageRotationValue.fromRawValue(sensorOrientation);

    if (rotation == null) return null;

    final format = InputImageFormatValue.fromRawValue(image.format.raw);
    if (format == null) return null;

    // Handle different image formats - most cameras use YUV420 which has multiple planes
    if (image.planes.isEmpty) return null;

    final plane = image.planes.first;

    return InputImage.fromBytes(
      bytes: plane.bytes,
      metadata: InputImageMetadata(
        size: Size(image.width.toDouble(), image.height.toDouble()),
        rotation: rotation,
        format: format,
        bytesPerRow: plane.bytesPerRow,
      ),
    );
  }

  @override
  void dispose() {
    _noFaceTimer?.cancel();
    _cameraController?.dispose();
    _faceDetector.close();
    _faceRecognition.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show error state
    if (_errorMessage != null) {
      final isPermissionError = _errorMessage!.toLowerCase().contains(
        'permission',
      );
      final isTimeoutError = _errorMessage!.contains('30 seconds');
      final isCameraError = _errorMessage!.toLowerCase().contains('camera');

      return Scaffold(
        appBar: AppBar(title: const Text("Face Detection Error")),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isPermissionError
                      ? Icons.security
                      : isTimeoutError
                      ? Icons.timer_off
                      : isCameraError
                      ? Icons.camera_alt_outlined
                      : Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  isPermissionError
                      ? "Camera Permission Required"
                      : isTimeoutError
                      ? "Face Detection Timeout"
                      : isCameraError
                      ? "Camera Access Error"
                      : "Face Detection Error",
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                if (isPermissionError) ...[
                  ElevatedButton.icon(
                    onPressed: () async {
                      await openAppSettings();
                    },
                    icon: const Icon(Icons.settings),
                    label: const Text("Open Settings"),
                  ),
                  const SizedBox(height: 12),
                ],
                if (isTimeoutError || isCameraError) ...[
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                        _isInitializing = true;
                      });
                      _initializeApp();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text("Try Again"),
                  ),
                  const SizedBox(height: 12),
                ],
                OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("Go Back"),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Show loading state
    if (_isInitializing ||
        _cameraController == null ||
        !_cameraController!.value.isInitialized) {
      return Scaffold(
        appBar: AppBar(title: const Text("Initializing Camera")),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text("Setting up camera and face detection..."),
            ],
          ),
        ),
      );
    }

    if (_capturedImageFile != null && _displayImage != null) {
      // A face was detected and a picture was taken.
      // Display the results and automatically close after a brief moment
      return Scaffold(
        appBar: AppBar(title: const Text("Face Captured Successfully")),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Display captured image with face detection overlay
              Expanded(
                child: FittedBox(
                  child: SizedBox(
                    width: _imageSize!.width,
                    height: _imageSize!.height,
                    child: CustomPaint(
                      painter: FaceDetectorPainter(
                        _detectedFaces,
                        _imageSize!,
                        _displayImage!,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Show recognition results
              if (_detectedFaces.isNotEmpty) ...[
                Text(
                  'Faces detected: ${_detectedFaces.length}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                for (final face in _detectedFaces)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(
                      face.isRecognized
                          ? '✓ ${face.user.name} (${(face.difference).toStringAsFixed(2)})'
                          : '? Unknown person',
                      style: TextStyle(
                        fontSize: 16,
                        color: face.isRecognized ? Colors.green : Colors.orange,
                      ),
                    ),
                  ),
              ],
              const SizedBox(height: 24),
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(_detectedFaces),
                    child: const Text("Continue"),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      // Reset to go back to live detection
                      setState(() {
                        _capturedImageFile = null;
                        _displayImage = null;
                        _detectedFaces = [];
                        _errorMessage = null;
                      });
                      _startNoFaceTimer();
                      _cameraController!.startImageStream(_processCameraImage);
                    },
                    child: const Text("Try Again"),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      );
    }

    // Show the live camera preview with guidance
    return Scaffold(
      appBar: AppBar(
        title: const Text("Position Your Face"),
        backgroundColor: Colors.black87,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          // Camera preview
          Positioned.fill(child: CameraPreview(_cameraController!)),
          // Face detection guidance overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.5),
                  width: 2,
                ),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.face, size: 100, color: Colors.white70),
                    SizedBox(height: 20),
                    Text(
                      'Position your face in the camera',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'The photo will be taken automatically',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Cancel button
          Positioned(
            top: 16,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Colors.black54,
              onPressed: () => Navigator.of(context).pop(),
              child: const Icon(Icons.close, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
