// import 'package:face_recognition/features/face_detection/presentation/pages/home.dart';
// import 'package:face_recognition/features/splash/presentation/pages/splash_screen.dart';
import 'package:face_recognition/view/face_detection_screen.dart';
import 'package:flutter/material.dart';

class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';

  static const String camera = '/camera';
  static const String gallery = '/gallery';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      // case home:
      //   return MaterialPageRoute(builder: (context) => );
      // case camera:
      //   return MaterialPageRoute(builder: (context) => const CameraPage());
      // case gallery:
      //   return MaterialPageRoute(builder: (context) => const GalleryPage());
      default:
        return MaterialPageRoute(builder: (context) => FaceDetectionScreen());
    }
  }
}
