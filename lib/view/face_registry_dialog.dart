import 'dart:typed_data';
import 'package:flutter/material.dart';

import '../models/face_registry.dart';

Future<void> showRegisterFaceDialog({
  required BuildContext context,
  required Uint8List imageBytes,
  required RegisteredUser person,
}) async {
  await showDialog(
    context: context,
    builder: (ctx) =>
        RegisterFaceDialog(imageBytes: imageBytes, person: person),
  );
}

class RegisterFaceDialog extends StatefulWidget {
  final Uint8List imageBytes;
  final RegisteredUser person;
  const RegisterFaceDialog({
    super.key,
    required this.imageBytes,
    required this.person,
  });

  @override
  State<RegisterFaceDialog> createState() => _RegisterFaceDialogState();
}

class _RegisterFaceDialogState extends State<RegisterFaceDialog> {
  late final TextEditingController _nameController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.person.name);
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        child: Card(
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.memory(widget.imageBytes),
                const SizedBox(height: 10),
                TextField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    hintText: 'Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () {
                    final newUser = RegisteredUser(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: _nameController.text,
                      embedding: widget.person.embedding,
                    );
                    FaceRegistry.listOfUsers.add(newUser);
                    Navigator.pop(context);
                  },
                  child: const Text("Save this face"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
