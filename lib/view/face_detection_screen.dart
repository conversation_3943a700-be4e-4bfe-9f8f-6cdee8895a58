import 'dart:async';
import 'dart:io';
import 'package:face_recognition/helper/face_painter.dart';
import 'package:face_recognition/helper/face_recognition.dart';
import 'package:face_recognition/helper/live_face_capture_view.dart';
import 'package:face_recognition/view/face_registry_dialog.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;

import 'dart:ui' as ui;

import '../helper/cordinates_translator.dart';
import '../models/face_registry.dart';

class FaceDetectionScreen extends StatefulWidget {
  const FaceDetectionScreen({super.key});

  @override
  State<FaceDetectionScreen> createState() => _FaceDetectionScreenState();
}

class _FaceDetectionScreenState extends State<FaceDetectionScreen> {
  final imagePicker = ImagePicker();
  final faceRecognition = FaceRecognition();
  img.Image? _viewImage;

  final faceDetector = FaceDetector(
    options: FaceDetectorOptions(performanceMode: FaceDetectorMode.accurate),
  );
  Size? _imageActualSize;
  int resizeImageWidth = 400;

  Future<List<Face>> detectFaces(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);
    final List<Face> faces = await faceDetector.processImage(inputImage);
    return faces;
  }

  List<FaceMatch> recognizedFaces = [];

  bool isProcessing = false;

  void pickAndProcess({ImageSource source = ImageSource.camera}) async {
    setState(() {
      _viewImage = null;
      _imageActualSize = null;
      isProcessing = true;
    });
    recognizedFaces.clear();
    final imageFile = await imagePicker.pickImage(source: source);
    if (imageFile != null) {
      // detect faces
      final List<Face> faces = await detectFaces(File(imageFile.path));
      final image = await img.decodeImageFile(imageFile.path);
      // if (image != null) return;
      for (final face in faces) {
        // crop image and generate facial vector
        final facialVector = await faceRecognition.recognizeFace(image!, face);
        final faceDetail = FaceRegistry.findFromList(
          facialVector,
          face.boundingBox,
        );
        recognizedFaces.add(faceDetail);
      }

      setState(() {
        _imageActualSize = Size(
          image!.width.toDouble(),
          image.height.toDouble(),
        );
        final resizeHeight = (image.height / image.width) * resizeImageWidth;
        _viewImage = img.copyResize(
          image,
          width: resizeImageWidth,
          height: resizeHeight.toInt(),
        );
        isProcessing = false;
      });
    }
  }

  // Launch automatic face capture with live camera
  void launchAutomaticFaceCapture() async {
    setState(() {
      isProcessing = true;
    });

    try {
      // Navigate to the live face capture view
      final List<FaceMatch>? capturedFaces = await Navigator.of(context)
          .push<List<FaceMatch>>(
            MaterialPageRoute(
              builder: (context) => const LiveFaceCaptureView(),
            ),
          );

      if (capturedFaces != null && capturedFaces.isNotEmpty) {
        // Process the captured faces
        setState(() {
          recognizedFaces = capturedFaces;
          isProcessing = false;
        });
      } else {
        setState(() {
          isProcessing = false;
        });
      }
    } catch (e) {
      setState(() {
        isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Face capture failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<ui.Image?> loadUiImage(img.Image? image) async {
    if (image == null) return null;
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromList(img.JpegEncoder().encode(image), (ui.Image img) {
      return completer.complete(img);
    });
    return completer.future;
  }

  Future registerFace() async {
    final unknownFace = recognizedFaces.firstWhere(
      (faceData) => !faceData.isRecognized,
    );
    final faceBounding = unknownFace.boundingRect;
    final resizedImageSize = Size(
      _viewImage!.width.toDouble(),
      _viewImage!.height.toDouble(),
    );
    final left = translateX(
      faceBounding.left,
      resizedImageSize,
      _imageActualSize!,
    ).toInt();
    final top = translateY(
      faceBounding.top,
      resizedImageSize,
      _imageActualSize!,
    ).toInt();
    final right = translateX(
      faceBounding.right,
      resizedImageSize,
      _imageActualSize!,
    ).toInt();
    final bottom = translateY(
      faceBounding.bottom,
      resizedImageSize,
      _imageActualSize!,
    ).toInt();
    final croppedImage = img.copyCrop(
      _viewImage!,
      x: left,
      y: top,
      width: right - left,
      height: bottom - top,
    );
    await showRegisterFaceDialog(
      context: context,
      imageBytes: img.JpegEncoder().encode(croppedImage),
      person: unknownFace.user,
    );
  }

  @override
  void initState() {
    faceRecognition.loadModel();
    super.initState();
  }

  @override
  void dispose() {
    faceRecognition.close();
    faceDetector.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(title: const Text("Face recognition app (POC)")),
      body: DefaultTextStyle(
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 15,
          color: Colors.black,
        ),
        child: SizedBox.expand(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Display results from automatic or manual capture
                if (recognizedFaces.isNotEmpty && _viewImage == null)
                  // Results from automatic face capture
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 64,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          "Face Detection Complete",
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "Faces detected: ${recognizedFaces.length}",
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        const SizedBox(height: 16),
                        // Show recognition results
                        for (final face in recognizedFaces)
                          Card(
                            child: ListTile(
                              leading: Icon(
                                face.isRecognized
                                    ? Icons.person
                                    : Icons.person_outline,
                                color: face.isRecognized
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              title: Text(face.user.name),
                              subtitle: Text(
                                face.isRecognized
                                    ? 'Recognized (${face.difference.toStringAsFixed(2)})'
                                    : 'Unknown person',
                              ),
                              trailing: face.isRecognized
                                  ? const Icon(
                                      Icons.verified,
                                      color: Colors.green,
                                    )
                                  : const Icon(
                                      Icons.help_outline,
                                      color: Colors.orange,
                                    ),
                            ),
                          ),
                      ],
                    ),
                  )
                else
                  // Display manual capture results or prompt
                  FutureBuilder(
                    future: loadUiImage(_viewImage),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Column(
                          children: [
                            if (recognizedFaces.isNotEmpty)
                              CustomPaint(
                                painter: FaceDetectorPainter(
                                  recognizedFaces,
                                  _imageActualSize!,
                                  snapshot.data!,
                                ),
                                child: SizedBox.fromSize(
                                  size: Size(
                                    _viewImage!.width.toDouble(),
                                    _viewImage!.height.toDouble(),
                                  ),
                                ),
                              ),
                            const SizedBox(height: 10),
                            Text(
                              recognizedFaces.isEmpty
                                  ? "No faces found in picture"
                                  : "faces found: ${recognizedFaces.length}",
                            ),
                          ],
                        );
                      }
                      return Padding(
                        padding: const EdgeInsets.all(15),
                        child: isProcessing
                            ? const Column(
                                children: [
                                  CircularProgressIndicator(),
                                  SizedBox(height: 16),
                                  Text("Processing..."),
                                ],
                              )
                            : const Column(
                                children: [
                                  Icon(
                                    Icons.camera_front,
                                    size: 64,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    "Ready for Face Detection",
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    "Use automatic detection for the best experience",
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                ],
                              ),
                      );
                    },
                  ),
                const SizedBox(height: 20),
                // Primary automatic face capture button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: isProcessing ? null : launchAutomaticFaceCapture,
                    icon: const Icon(Icons.camera_front, size: 24),
                    label: const Text(
                      "Start Automatic Face Detection",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Secondary manual options
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: isProcessing
                      ? null
                      : () {
                          showDialog(
                            context: context,
                            builder: (ctx) {
                              return Center(
                                child: Material(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  child: SizedBox(
                                    width: 300,
                                    child: ListView(
                                      shrinkWrap: true,
                                      children: [
                                        ListTile(
                                          leading: const Icon(Icons.camera_alt),
                                          title: const Text("Manual Camera"),
                                          subtitle: const Text(
                                            "Take photo manually",
                                          ),
                                          onTap: () {
                                            Navigator.pop(ctx);
                                            pickAndProcess();
                                          },
                                        ),
                                        const Divider(height: 0),
                                        ListTile(
                                          leading: const Icon(
                                            Icons.photo_library,
                                          ),
                                          title: const Text("Photo Library"),
                                          subtitle: const Text(
                                            "Select from gallery",
                                          ),
                                          onTap: () {
                                            Navigator.pop(ctx);
                                            pickAndProcess(
                                              source: ImageSource.gallery,
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        },
                  child: const Text("Manual Options"),
                ),
                if (recognizedFaces.isNotEmpty &&
                    recognizedFaces.any(
                      (faceData) => !faceData.isRecognized,
                    )) ...[
                  const SizedBox(height: 30),
                  const Text("Unknown face found... click below to save it"),
                  const SizedBox(height: 10),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: const RoundedRectangleBorder(),
                    ),
                    onPressed: registerFace,
                    child: const Text("SAVE"),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
