// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Basic app structure smoke test', (WidgetTester tester) async {
    // Build a simple test app without loading the face recognition model
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          appBar: AppBar(title: const Text("Face recognition app (POC)")),
          body: const Center(child: Text("Face Recognition App")),
        ),
      ),
    );

    // Verify that the app title is displayed
    expect(find.text('Face recognition app (POC)'), findsOneWidget);
    expect(find.text('Face Recognition App'), findsOneWidget);
  });
}
